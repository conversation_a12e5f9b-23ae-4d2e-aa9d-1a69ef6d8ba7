/* Sign-in button text */
"Sign in" = "Fazer login";

/* Long form sign-in button text */
"Sign in with Google" = "Fazer login com o Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Cancelar";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Configurações";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Não foi possível fazer login na conta";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Seu administrador exige que você defina uma senha neste dispositivo para acessar esta conta. Defina uma senha e tente novamente.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "O dispositivo não está em conformidade com a política de segurança definida pelo administrador.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Conectar-se ao app Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Para proteger os dados da sua organização, você precisa se conectar ao app Device Policy antes de fazer login.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Conectar";
