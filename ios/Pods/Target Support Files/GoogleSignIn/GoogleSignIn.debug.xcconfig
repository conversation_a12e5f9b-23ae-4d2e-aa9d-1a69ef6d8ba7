CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
COMBINE_HIDPI_IMAGES = NO
CONFIGURATION_BUILD_DIR = ${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn
DEFINES_MODULE = YES
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 GID_SDK_VERSION=7.1.0
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Private" "${PODS_ROOT}/Headers/Private/GoogleSignIn" "${PODS_ROOT}/Headers/Public" "${PODS_TARGET_SRCROOT}"
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth/GTMAppAuth.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/AppAuth/AppAuth.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/GTMSessionFetcher/GTMSessionFetcher.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_DEVELOPMENT_LANGUAGE = ${DEVELOPMENT_LANGUAGE}
PODS_ROOT = ${SRCROOT}
PODS_TARGET_SRCROOT = ${PODS_ROOT}/GoogleSignIn
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
PRODUCT_BUNDLE_IDENTIFIER = org.cocoapods.${PRODUCT_NAME:rfc1034identifier}
SKIP_INSTALL = YES
SWIFT_INCLUDE_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth"
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
